import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/test-*',
          '/debug'
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/test-*',
          '/debug'
        ],
      },
    ],
    sitemap: 'https://www.flerid.in/sitemap.xml',
    host: 'https://www.flerid.in'
  }
}
